-- Migration: Drop transaction_summary_report table and keep only transaction_summary_report_detail
-- Date: 2025-07-23
-- Description: Removes the main transaction_summary_report table since we only use transaction_summary_report_detail

-- Step 1: Remove foreign key constraint from detail table
ALTER TABLE transaction_summary_report_detail 
DROP CONSTRAINT IF EXISTS transaction_summary_report_detail_report_id_fkey;

-- Step 2: Drop dependent views that reference the main table
DROP VIEW IF EXISTS v_transaction_summary_workflow;

-- Step 3: Drop functions that reference the main table
DROP FUNCTION IF EXISTS get_merchant_summary_stats(DATE, DATE);

-- Step 4: Drop triggers on the main table
DROP TRIGGER IF EXISTS trigger_update_transaction_summary_report_timestamp ON transaction_summary_report;

-- Step 5: Drop the main transaction_summary_report table
DROP TABLE IF EXISTS transaction_summary_report CASCADE;

-- Step 6: Drop the shared trigger function if no longer needed
-- (Keep it commented out in case other tables use it)
-- DROP FUNCTION IF EXISTS update_transaction_summary_report_timestamp();

-- Step 7: Add any missing columns to detail table that might be needed
-- (Add report metadata columns if they don't exist)
ALTER TABLE transaction_summary_report_detail 
ADD COLUMN IF NOT EXISTS batch_id VARCHAR(100);

ALTER TABLE transaction_summary_report_detail 
ADD COLUMN IF NOT EXISTS report_date DATE;

ALTER TABLE transaction_summary_report_detail 
ADD COLUMN IF NOT EXISTS report_time TIME;

ALTER TABLE transaction_summary_report_detail 
ADD COLUMN IF NOT EXISTS running_number VARCHAR(50);

ALTER TABLE transaction_summary_report_detail 
ADD COLUMN IF NOT EXISTS processed_files TEXT[];

-- Step 8: Create indexes for the new columns
CREATE INDEX IF NOT EXISTS idx_transaction_summary_report_detail_batch_id 
ON transaction_summary_report_detail(batch_id);

CREATE INDEX IF NOT EXISTS idx_transaction_summary_report_detail_report_date 
ON transaction_summary_report_detail(report_date);

-- Step 9: Update the trigger to work with detail table only
CREATE TRIGGER trigger_update_transaction_summary_report_detail_timestamp
    BEFORE UPDATE ON transaction_summary_report_detail
    FOR EACH ROW
    EXECUTE FUNCTION update_transaction_summary_report_timestamp();

-- Step 10: Create a simplified view for the detail table
CREATE OR REPLACE VIEW v_transaction_summary_detail_workflow AS
SELECT 
    batch_id,
    report_date,
    COUNT(*) as merchant_summary_count,
    SUM(CASE WHEN is_transfer = 1 THEN 1 ELSE 0 END) as transferred_count,
    SUM(CASE WHEN is_transfer = 0 THEN 1 ELSE 0 END) as pending_count,
    SUM(final_net_amount) as total_final_net_amount,
    MIN(create_dt) as first_created,
    MAX(update_dt) as last_updated
FROM transaction_summary_report_detail
WHERE batch_id IS NOT NULL
GROUP BY batch_id, report_date
ORDER BY report_date DESC, first_created DESC;

-- Step 11: Add comments for documentation
COMMENT ON TABLE transaction_summary_report_detail IS 'Detail table storing merchant-level transaction summary data (main table removed)';
COMMENT ON COLUMN transaction_summary_report_detail.batch_id IS 'Unique identifier for the processing batch (moved from main table)';
COMMENT ON COLUMN transaction_summary_report_detail.report_date IS 'Date of the report (moved from main table)';

-- Verification queries
SELECT 'Migration completed successfully' as status;

-- Show the updated table structure
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'transaction_summary_report_detail' 
ORDER BY ordinal_position;
