# Transaction Summary Screen

## Overview
The Transaction Summary screen provides a comprehensive view of transaction data with both summary and detailed views. It replicates the functionality shown in the original Windows application design.

## Features

### 1. Header Controls
- **Search**: Refresh and load transaction data
- **Cancel**: Reset form to default values
- **Approve**: Approve the current transaction summary (requires create permission)
- **Close**: Navigate back to previous screen
- **Print**: Open print dialog for current view
- **Export**: Export current data as CSV file

### 2. Filter Controls
- **Print Type**: Toggle between "Transfer" and "Pending" views
- **Running Number**: Editable transaction reference number
- **Date**: Date picker for filtering transactions
- **Transaction Number**: Read-only display of running number

### 3. Summary Section
Displays key financial metrics:
- **Transfer Today**: Total amount transferred today
- **Pending Transfer**: Amount pending transfer (highlighted in red)
- **Total Amount**: Grand total (prominently displayed in black box)

### 4. Tab Navigation
- **Transfer Today**: Shows summary view with bank/merchant details
- **Transaction**: Shows detailed transaction breakdown

### 5. Summary Table (Transfer Today Tab)
Columns:
- Bank: Full bank name in Thai
- Group: Merchant group classification
- MerchantID: Unique merchant identifier
- Account No: Bank account number
- Account Name: Account holder name
- Amount: Transaction amount (formatted as Thai currency)
- Fax: Contact fax number
- Email: Contact email address

### 6. Transaction Details Table (Transaction Tab)
Columns:
- Trn Date: Transaction date
- Service A/C: Service account number
- MID: Merchant ID
- Name: Merchant name
- TID: Terminal ID
- Trn Amt: Transaction amount
- Sum MDR: Merchant Discount Rate total
- VAT: Value Added Tax
- Net Amt: Net amount after deductions
- Withhold TAX: Withholding tax amount
- Reimbursement Fee: Reimbursement charges
- Service Fee: Service charges
- Net Amt CUP: Net amount for CUP transactions
- Business TAX: Business tax amount
- TID No: Terminal number

## Technical Implementation

### Data Types
```typescript
interface TransactionSummaryItem {
  id: number;
  bank: string;
  group: string;
  merchantId: string;
  accountNo: string;
  accountName: string;
  amount: number;
  fax?: string;
  email?: string;
}

interface TransactionDetailItem {
  id: number;
  trnDate: string;
  serviceAC: string;
  mid: string;
  name: string;
  tid: string;
  trnAmt: number;
  sumMDR: number;
  vat: number;
  netAmt: number;
  withholdTax: number;
  reimbursementFee: number;
  serviceFee: number;
  netAmtCUP: number;
  businessTax: number;
  tidNo: number;
}
```

### Key Features
- **Role-based Access Control**: Uses `RoleBasedComponent` for permission-based UI
- **Responsive Design**: Tables are horizontally scrollable on smaller screens
- **Mock Data**: Currently uses mock data for demonstration
- **Export Functionality**: CSV export for both summary and detail views
- **Print Support**: Browser print functionality
- **Thai Language Support**: Proper formatting for Thai text and currency

### Styling
- Uses Tailwind CSS for consistent styling
- Blue header matching Windows application theme
- Alternating row colors for better readability
- Proper spacing and borders for table elements
- Responsive design with minimum column widths

### Navigation
- Accessible via `/transaction-summary` route
- Added to sidebar navigation with 📊 icon
- Protected route requiring authentication

## Future Enhancements
1. **Real Data Integration**: Replace mock data with actual database queries
2. **Advanced Filtering**: Add more filter options (date range, merchant, amount)
3. **Pagination**: Add pagination for large datasets
4. **Real-time Updates**: Auto-refresh functionality
5. **PDF Export**: Add PDF export option alongside CSV
6. **Chart Visualization**: Add charts for summary data
7. **Audit Trail**: Track user actions and approvals

## Usage
1. Navigate to Transaction Summary from the sidebar
2. Select desired date and running number
3. Click Search to load data
4. Switch between Summary and Transaction tabs
5. Use Export to download data as CSV
6. Use Print for hard copy reports
7. Click Approve to approve the summary (if authorized)

## Permissions
- **Read Access**: View transaction summaries and details
- **Create Permission**: Required for Approve functionality
- **Role-based UI**: Different interface elements based on user role
